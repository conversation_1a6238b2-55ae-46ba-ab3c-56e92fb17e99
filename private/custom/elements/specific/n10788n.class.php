<?php
namespace nl\actinum\custom\elements\specific {




/*[GENERATEDBYBUILDER_CLASSNAME*/
class n10788n extends \nl\actinum\custom\elements\generic\Html
/*GENERATEDBYBUILDER_CLASSNAME]*/
 implements \nl\actinum\framework\application\interfaces\IHasCustomClientSideCode,
    \nl\actinum\framework\application\interfaces\IHasDataForAdd,
    \nl\actinum\framework\application\interfaces\IHasDataForView,
    \nl\actinum\framework\application\interfaces\IHasDataForEdit,
    \nl\actinum\framework\application\interfaces\IHasDataForSave,
    \nl\actinum\framework\application\interfaces\IHasSettings
                                                                                                                            {
/*[GENERATEDBYBUILDER*/

    public static $html = 'tabel allergenen';
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n10788n';
    public static $parentElementid = 'n9359n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    public static $childrenElementIds = array (
);
/*GENERATEDBYBUILDER]*/



    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.CustomJs.n10788n'] =
<<<'JS'
Actinum.Application.CustomElements.n10788n = new Class({ Extends: Actinum.Application.Elements.Html,

    rows: [],
    
    registerListeners: function registerListeners(){
        this.parent();
        if (this.getMode() != 'view'){
            this.addListeners(['n9359n:tabselected'], function(){
                this.updateAllergenenIndicatie();
            }.bind(this));
        }
    },

    neemOverVanSuggesties: function neemOverVanSuggesties(){
        this.rows.each(function(row){
            row.neemover();
        }.bind(this));
        this.notifyDataChanged();
        this.reRender();
    },

    alleWaardenLegen: function alleWaardenLegen(){
        this.rows.each(function(row){
            row.legen();
        }.bind(this));
        this.notifyDataChanged();
        this.reRender();
    },

    updateAllergenenIndicatie: function updateAllergenenIndicatie(){
        //ajax call met lijst uit pbg van vorige tab.
        var grondstofids = [];

        var fetchfromdb = false; //als de data nog niet gerenderd is, dan moet e.e.a. van de server gehaald worden.
        if ($N('n11904n').rendered){
            $N('n11905n').getGridrows().each(function(row){
                if (row.getMetadata().state != 'deleted'){
                    grondstofids.push(row.getCells()['n11915n'].getData().raw);
                }
            });
        }
        else{
            fetchfromdb = true;
        }

        var actionparams = {
            'requestedelement' : this.elementid,
            'methodname' : 'getAllergenenindicatie',
            'arguments' : [
                grondstofids,
                fetchfromdb,
                Actinum.application.getCurrentRecordId()
            ]
        };
        var action = new Actinum.Application.Actions.callUserFunctionAction(actionparams, function(response){
            this._updateAllergenenIndicatie(response.allergenenindicatie);
        }.bind(this));
        action.run();
    },

    _updateAllergenenIndicatie: function _updateAllergenenIndicatie(allergenenindicatie){
        this.rows.each(function(row){
            var markbool = allergenenindicatie.contains(row.getId());
            row.markAllergen(markbool);
        }.bind(this))
    },

    renderadd: function renderadd(){
        var el = this.parent();
        el.addClass('Grid');
        this.rendertable().inject(el);
        this._updateAllergenenIndicatie(this.getData().allergenenindicatie);
        return el;
    },

    renderedit: function renderedit(){
        var el = this.parent();
        el.addClass('Grid');
        this.rendertable().inject(el);
        this._updateAllergenenIndicatie(this.getData().allergenenindicatie);
        return el;
    },

    renderview: function renderview(){
        var el = this.parent();
        el.addClass('Grid');
        this.rendertable().inject(el);
        this._updateAllergenenIndicatie(this.getData().allergenenindicatie);
        return el;
    },

    rendertable: function rendertable(){
        var container = new Element('div');
        var tablediv = new Element('div', {'class' : 'grid'});

        var table = new Element('table', {'class' : 'LinkgridContent collapseall', 'style' : 'width: 100%'});
        var tbody = new Element('tbody');

        var rows_array = Object.values(this.getData().rows);
        rows_array.sort(function(a, b){
            if (a['_ordering'] == b['_ordering']) {
                return 0;
            }
            return (a['_ordering'] < b['_ordering']) ? -1 : 1;
        });

        rows_array.each(function(rowdata_raw) {
            var row = new Actinum.Application.CustomElements.n10788n_row(this, rowdata_raw, this.getData().allergennotifications, this.getData().contaminationtypes);
            this.rows.push(row);
            row.render().inject(tbody);
        }.bind(this));
        
        var thead = new Element('thead', {'class' : 'tabHeader'}).adopt(
            new Element('th', {'style' : 'width: 15%;', 'class' : 'tabHeader tabHeaderLeft', 'text' : 'Naam'}),
            new Element('th', {'style' : 'width:  3%;', 'class' : 'tabHeader tabHeaderCenter', 'text' : ' '}),
            new Element('th', {'style' : 'width: 25%;', 'class' : 'tabHeader tabHeaderCenter', 'text' : 'Melding'}),
        )
           
        if (this.getData().ppmoption !== 3) {   
            thead.adopt(
                new Element('th', {'style' : 'width: 33%;', 'class' : 'tabHeader tabHeaderRight', 'text' : 'PPM'}), 
            )
        }
            
        if (this.getData().ppmoption === 3) {
            thead.adopt(
                new Element('th', {'style' : 'width: 33%;', 'class' : 'tabHeader tabHeaderCenter', 'text' : 'Eiwitgehalte \n allergeen (%)'}),
                new Element('th', {'style' : 'width: 33%;', 'class' : 'tabHeader tabHeaderCenter', 'text' : 'Type besmetting'}),
                new Element('th', {'style' : 'width: 33%;', 'class' : 'tabHeader tabHeaderCenter', 'text' : 'PPM kruisbesmetting'}),
                new Element('th', {'style' : 'width: 33%;', 'class' : 'tabHeader tabHeaderCenter', 'text' : 'Gewicht particulate (g)'}),
                new Element('th', {'style' : 'width: 33%;', 'class' : 'tabHeader tabHeaderRight', 'text' : 'Eiwitgehalte \n particulate (%)'}),
            )
        }
        
        table.adopt(
            thead,
            tbody
        );
        
        table.inject(tablediv);
        tablediv.inject(container);


        return container;
    },

    hasChanged: function hasChanged(){
        //als er iets bij of af gegaan is
        var newdata = this.getSubmitData();
        return (Object.getLength(newdata) > 0);
    },

    getSubmitData: function getSubmitData() {
        var _orig = this.privates.dataorig.rows; //array containing checked id's.
        var _new = this.getData().rows; //current data, because we might have added new values dynamically.

        var result = [];

        var check = Object.each(_new, function(item) {
            var newAllergenNotification = item.record.allergennotification ? item.record.allergennotification.id : null;
            var oldAllergenNotification = _orig[item.id].record.allergennotification ? _orig[item.id].record.allergennotification.id : null;
            
            var newTypeOfContamination = item.record.type_of_contamination ? item.record.type_of_contamination.id : null;
            var oldTypeOfContamination = _orig[item.id].record.type_of_contamination ? _orig[item.id].record.type_of_contamination.id : null;
            
            if(
                newAllergenNotification != oldAllergenNotification ||
                item.record.ppm != _orig[item.id].record.ppm || 
                item.record.allergen_protein_content != _orig[item.id].record.allergen_protein_content  ||
                newTypeOfContamination != oldTypeOfContamination ||
                item.record.ppm_cross_contamination != _orig[item.id].record.ppm_cross_contamination  ||
                item.record.particulate_weight != _orig[item.id].record.particulate_weight  ||
                item.record.particulate_protein_content != _orig[item.id].record.particulate_protein_content
            ) {
                result.push(item);
            }
        });

        return result;
    }, 
    
    checkRequiredFields: function checkRequiredFields(){
        var errors = [];
        
        var rows = this.getData().rows; //current data, because we might have added new values dynamically.
        
        Object.each(rows, function(item) {
            if(item.record.allergennotification) {
                if(item.record.allergennotification.id == 1) {
                    if (item.record.allergen_protein_content == '') {
                        errors.push("'Eiwitgehalte allergeen' voor allergeen " + item.name + " is een verplicht veld.");
                    }
                }
                
                if(item.record.allergennotification.id == 3) {
                    if (item.record.type_of_contamination == '' || item.record.type_of_contamination == null) {
                        errors.push("'Type besmetting' voor allergeen " + item.name + " is een verplicht veld.");
                    }
                    
                    if (item.record.type_of_contamination) {
                        if (item.record.type_of_contamination.id == 1) {
                            if (item.record.ppm_cross_contamination == '' || item.record.ppm_cross_contamination == null) {
                                errors.push("'PPM kruisbesmetting' voor allergeen " + item.name + " is een verplicht veld.");
                            }
                        }
                        
                        else if (item.record.type_of_contamination.id == 2) {
                            if (item.record.particulate_weight == '' || item.record.particulate_weight == null) {
                                errors.push("'Gewicht particulate' voor allergeen " + item.name + " is een verplicht veld.");
                            }
                            
                            if (item.record.particulate_protein_content == '' || item.record.particulate_protein_content == null) {
                                errors.push("'Eiwitgehalte particulate' voor allergeen " + item.name + " is een verplicht veld.");
                            }
                        }
                    }
                }
            }
        });
        
        return errors;
    },

});


Actinum.Application.CustomElements.n10788n_row = new Class({

    element: null,
    data: null,

    meldingelement: null,
    ppmelement: null,
    allergen_protein_content_element: null,
    type_of_contamination_element: null,
    ppm_cross_contamination_element: null,
    particulate_weight_element: null,
    particulate_protein_content_element: null,
    allergenmark: false,

    initialize: function initialize(element, data, allergennotifications, contaminationtypes){
        this.element = element;
        this.data = data;
        this.allergennotifications = allergennotifications;
        this.contaminationtypes = contaminationtypes;
    },

    neemover: function neemover(){
        if (this.allergenmark){
            var data = this.element.getData();
            data.rows[this.data.id].record.allergennotification = {
                'id' : 1,
                'name' : 'Aanwezig'
            };
            this.element.setData(data);
        } else {
            var data = this.element.getData();
            data.rows[this.data.id].record.allergennotification = {
                'id' : 2,
                'name' : 'Afwezig'
            };
            this.element.setData(data);
        }
    },

    legen: function legen(){
        var data = this.element.getData();

        data.rows[this.data.id].record.ppm = '';
        data.rows[this.data.id].record.allergennotification = {
            'id' : null,
            'name' : ''
        };

        this.element.setData(data);
    },

    markAllergen: function markAllergen(b){
        this.allergenmark = b;
        this.allergenMark.empty();
        if (b){
            var img = new Element('img', {
                'src':  'assets/images/warn.png',
                'width' : '15px',
                'height' : '15px',
                'title' : 'Let op: volgens de grondstoffen bevat dit inkoopartikel dit allergeen.'
            });
            this.allergenMark.adopt(img);
        }
    },

   updateVisiblityPPM: function updateVisiblityPPM(){
       var data = this.element.getData();
       
       if (data.ppmenabled == true){
           this.ppmelement.show();
       }
       else{
           this.ppmelement.hide();
       }
   },

    getId: function getId(){
        return this.data.id;
    },

    render: function render(){
        if(this.element.getMode() != 'view') {
            var meldingelement = this.meldingelement = new Element('select', {
                'type' : 'text',
                'style' : 'padding: 5px; border-radius: 3px; border: 1px solid #d6d6d6;',
                'events' : {
                    'change': function(e) {
                        var data = this.element.getData();
                        
                         data.rows[this.data.id].record.allergennotification = {
                                'id' : e.target.value,
                                'name' : e.target.get('text')
                            };
                         
                         if(e.target.value == ''){
                            this.disableElement(this.allergen_protein_content_element, 'allergen_protein_content');
                            this.disableElement(this.type_of_contamination_element, 'type_of_contamination');
                            this.disableElement(this.ppm_cross_contamination_element, 'ppm_cross_contamination');
                            this.disableElement(this.particulate_weight_element, 'particulate_weight');
                            this.disableElement(this.particulate_protein_content_element, 'particulate_protein_content');
                         }
                        
                        if (e.target.value == 1) { // aanwezig
                            this.enableElement(this.allergen_protein_content_element, 'allergen_protein_content');
                            this.disableElement(this.type_of_contamination_element, 'type_of_contamination');
                            this.disableElement(this.ppm_cross_contamination_element, 'ppm_cross_contamination');
                            this.disableElement(this.particulate_weight_element, 'particulate_weight');
                            this.disableElement(this.particulate_protein_content_element, 'particulate_protein_content');
                        }
                        else if (e.target.value == 2) { // afwezig
                            this.disableElement(this.allergen_protein_content_element, 'allergen_protein_content');
                            this.disableElement(this.type_of_contamination_element, 'type_of_contamination');
                            this.disableElement(this.ppm_cross_contamination_element, 'ppm_cross_contamination');
                            this.disableElement(this.particulate_weight_element, 'particulate_weight');
                            this.disableElement(this.particulate_protein_content_element, 'particulate_protein_content');
                        }
                        else if (e.target.value == 3) { // kan bevatten
                            this.disableElement(this.allergen_protein_content_element, 'allergen_protein_content');
                            this.enableElement(this.type_of_contamination_element, 'type_of_contamination');
                            this.disableElement(this.ppm_cross_contamination_element, 'ppm_cross_contamination');
                            this.disableElement(this.particulate_weight_element, 'particulate_weight');
                            this.disableElement(this.particulate_protein_content_element, 'particulate_protein_content');
                            
                            var particulateContaminationElement = this.contaminationtypes.filter(function(item) {
                                return item.name.toLowerCase() === 'particulates';
                            })[0];
            
                            var particulateContaminationElementId = particulateContaminationElement ? particulateContaminationElement.id : null;
                            
                                this.type_of_contamination_element.set('value', particulateContaminationElementId);
                                // Trigger the change event after setting the value
                                  data.rows[this.data.id].record.type_of_contamination = {
                                        'id' : particulateContaminationElement.id,
                                        'name' : particulateContaminationElement.name
                                    };
                                  
                                  this.particulate_weight_element.set('value', null);
                                  this.particulate_protein_content_element.set('value', null);
                                  
                                this.type_of_contamination_element.fireEvent('change', { target: this.type_of_contamination_element });
                                this.particulate_weight_element.fireEvent('change', { target: this.particulate_weight_element });
                                this.particulate_protein_content_element.fireEvent('change', { target: this.particulate_protein_content_element });
                        }
                        else if (e.target.value == 4) { // onbekend
                            this.disableElement(this.allergen_protein_content_element, 'allergen_protein_content');
                            this.disableElement(this.type_of_contamination_element, 'type_of_contamination');
                            this.disableElement(this.ppm_cross_contamination_element, 'ppm_cross_contamination');
                            this.disableElement(this.particulate_weight_element, 'particulate_weight');
                            this.disableElement(this.particulate_protein_content_element, 'particulate_protein_content');
                        }
                        
                        this.element.setData(data);
                    }.bind(this)
                }
            });

            var option = new Element('option', {
                'text' : '',
                'value' : ''
            });
            meldingelement.adopt(option);
            
            var data = this.element.getData();
            var activatedAllergenPolicy2024 = data.allergen_policy_2024
        
        
            this.allergennotifications.each(function(item){
                var disabled = activatedAllergenPolicy2024 && item.name.toLowerCase() === 'onbekend';
                var option = new Element('option', {
                    'text' : item.name,
                    'value' : item.id,
                    'disabled': disabled
                });
                meldingelement.adopt(option);
            });

            var meldingelement_value = this.data.record['allergennotification'] ? this.data.record['allergennotification']['id'] : null;
            if (meldingelement_value){
                meldingelement.set('value', meldingelement_value);
            }


            var ppmelement = this.ppmelement = new Element('input', {
                'type' : 'text',
                'value':  this.data.record['ppm'],
                // 'style' : 'padding: 5px; border-radius: 3px; border: 1px solid #d6d6d6; background: #f2f2f2;',
                'style' : 'padding: 5px; border-radius: 3px; border: 1px solid #d6d6d6;',
                // 'readonly' : true,
                // 'disabled' : true,
                'events' : {
                    'change': function(e) {
                        var data = this.element.getData();
                        data.rows[this.data.id].record.ppm = e.target.value;
                        this.element.setData(data);
                    }.bind(this)
                }
            });
            ppmelement.addEvent('keypress', Actinum.DefaultEvents.isFloat);
            
            // Eiwitgehalte allergeen
            var allergen_protein_content_element = this.allergen_protein_content_element = new Element('input', {
                'type' : 'text',
                'value':  this.data.record['allergen_protein_content'],
                'style' : 'padding: 5px; border-radius: 3px; border: 1px solid #d6d6d6; width: 120px;',
                'disabled' : meldingelement_value !== 1,
                'required' : true,
                'events' : {
                    'change': function(e) {
                       var rawValue = e.target.value.trim();

                        // Check for negative values
                        if (parseFloat(rawValue) < 0) {
                            e.target.value = this.data.record['allergen_protein_content'];
                            Stratcom.notify('error', { 'message': 'Negatieve waarden zijn niet toegestaan.' });
                            return; 
                        }
                        
                        var formattedValue = rawValue;
                        
                        if (rawValue.indexOf('.') !== -1) {
                            var parts = rawValue.split('.');
                            var integerPart = parts[0];
                            var decimalPart = parts[1] || ''; // als gebruiker “12.” invoert, is parts[1] = ''
                            
                            if (decimalPart.length > 2) {
                                // Afkappen op 2 cijfers achter de komma (niet afronden, enkel trimmen)
                                decimalPart = decimalPart.substring(0, 2);
                            }
                            // Alleen weer de '.' toevoegen als er een decimaaldeel is (ook als dat nu ’00’ of korter is)
                            if (decimalPart.length > 0) {
                                formattedValue = integerPart + '.' + decimalPart;
                            } else {
                                // Gebruiker had bijvoorbeeld “15.” of “15.0” of “15.00” zonder extra decimals => integer houden
                                formattedValue = integerPart;
                            }
                        }
                        
                        e.target.value = formattedValue;
                        
                        var data = this.element.getData();
                        data.rows[this.data.id].record.allergen_protein_content = formattedValue;
                        this.element.setData(data);
                    }.bind(this),
                    'blur': function(e) {
                        if (this.element.getData().rows[this.data.id].record.allergennotification.id == 1 && e.target.value == '') {
                            this.allergen_protein_content_element.addClass('forminputerror');
                        }
                    }.bind(this),
                    'focus': function(e) {
                        this.allergen_protein_content_element.removeClass('forminputerror');
                    }.bind(this)
                }
            });
            allergen_protein_content_element.addEvent('keypress', Actinum.DefaultEvents.isFloat);
            
            // Type besmetting
            var type_of_contamination_element = this.type_of_contamination_element = new Element('select', {
                'style' : 'padding: 5px; border-radius: 3px; border: 1px solid #d6d6d6;',
                'disabled' : meldingelement_value !== 3,
                'required' : true,
                'events' : {
                    'change': function(e) {
                        var data = this.element.getData();
                        data.rows[this.data.id].record.type_of_contamination = {
                            'id' : e.target.value,
                            'name' : e.target.get('text')
                        };
                        this.element.setData(data);
                        
                        if (e.target.value == 1) { // homogeen
                            this.enableElement(this.ppm_cross_contamination_element, 'ppm_cross_contamination');
                            this.disableElement(this.particulate_weight_element, 'particulate_weight');
                            this.disableElement(this.particulate_protein_content_element, 'particulate_protein_content');
                        }
                        else if (e.target.value == 2) { // particulates
                            this.disableElement(this.ppm_cross_contamination_element, 'ppm_cross_contamination');
                            this.enableElement(this.particulate_weight_element, 'particulate_weight');
                            this.enableElement(this.particulate_protein_content_element, 'particulate_protein_content');
                        }
                        
                    }.bind(this),
                    'blur': function(e) {
                        if (this.element.getData().rows[this.data.id].record.allergennotification.id == 1 && e.target.value == '') {
                            this.allergen_protein_content_element.addClass('forminputerror');
                        }
                    }.bind(this),
                    'focus': function(e) {
                        this.allergen_protein_content_element.removeClass('forminputerror');
                    }.bind(this)
                }
            });
            
            var option = new Element('option', {
                'text' : '',
                'value' : ''
            });
            
            var disabledOption = new Element('option', {
                'text' : 'Selecteer een optie',
                'value' : '',
                'disabled': true,
                'selected': true
            });
            type_of_contamination_element.adopt(disabledOption);
            this.contaminationtypes.each(function(item){
                var option = new Element('option', {
                    'text' : item.name,
                    'value' : item.id
                });
                type_of_contamination_element.adopt(option);
            });
            
            
            var type_of_contamination_element_value = this.data.record['type_of_contamination'] ? this.data.record['type_of_contamination']['id'] : null;
            if (type_of_contamination_element_value){
                type_of_contamination_element.set('value', type_of_contamination_element_value);
            }
            
            // PPM kruisbesmetting
            var ppm_cross_contamination_element = this.ppm_cross_contamination_element = new Element('input', {
                'type' : 'text',
                'value':  this.data.record['ppm_cross_contamination'],
                'style' : 'padding: 5px; border-radius: 3px; border: 1px solid #d6d6d6; width: 120px;',
                'disabled' : meldingelement_value !== 3 || type_of_contamination_element_value !== 1,
                'required' : true,
                'events' : {
                    'change': function(e) {
                        
                        var rawValue = e.target.value.trim();

                        // Check for negative values
                        if (parseFloat(rawValue) < 0) {
                            e.target.value = this.data.record['allergen_protein_content'];
                            Stratcom.notify('error', { 'message': 'Negatieve waarden zijn niet toegestaan.' });
                            return; 
                        }
                        
                        var formattedValue = rawValue;
                        
                        if (rawValue.indexOf('.') !== -1) {
                            var parts = rawValue.split('.');
                            var integerPart = parts[0];
                            var decimalPart = parts[1] || ''; // als gebruiker “12.” invoert, is parts[1] = ''
                            
                            if (decimalPart.length > 2) {
                                // Afkappen op 2 cijfers achter de komma (niet afronden, enkel trimmen)
                                decimalPart = decimalPart.substring(0, 2);
                            }
                            // Alleen weer de '.' toevoegen als er een decimaaldeel is (ook als dat nu ’00’ of korter is)
                            if (decimalPart.length > 0) {
                                formattedValue = integerPart + '.' + decimalPart;
                            } else {
                                // Gebruiker had bijvoorbeeld “15.” of “15.0” of “15.00” zonder extra decimals => integer houden
                                formattedValue = integerPart;
                            }
                        }
                        
                        e.target.value = formattedValue;
                        
                        var data = this.element.getData();
                        data.rows[this.data.id].record.ppm_cross_contamination = formattedValue
                        this.element.setData(data);
                    }.bind(this),
                    'blur': function(e) {
                        if (this.element.getData().rows[this.data.id].record.allergennotification.id == 1 && e.target.value == '') {
                            this.allergen_protein_content_element.addClass('forminputerror');
                        }
                    }.bind(this),
                    'focus': function(e) {
                        this.allergen_protein_content_element.removeClass('forminputerror');
                    }.bind(this)
                }
            });
            ppm_cross_contamination_element.addEvent('keypress', Actinum.DefaultEvents.isFloat);
            
            // Gewicht particulate
            var particulate_weight_element = this.particulate_weight_element = new Element('input', {
                'type' : 'text',
                'value':  this.data.record['particulate_weight'],
                'style' : 'padding: 5px; border-radius: 3px; border: 1px solid #d6d6d6; width: 120px;',
                'disabled' : meldingelement_value !== 3 || type_of_contamination_element_value !== 2,
                'required' : true,
                'events' : {
                    'change': function(e) {
                        var rawValue = e.target.value.trim();

                        // Check for negative values
                        if (parseFloat(rawValue) < 0) {
                            e.target.value = this.data.record['allergen_protein_content'];
                            Stratcom.notify('error', { 'message': 'Negatieve waarden zijn niet toegestaan.' });
                            return; 
                        }
                        
                        var formattedValue = rawValue;
                        
                        if (rawValue.indexOf('.') !== -1) {
                            var parts = rawValue.split('.');
                            var integerPart = parts[0];
                            var decimalPart = parts[1] || ''; // als gebruiker “12.” invoert, is parts[1] = ''
                            
                            if (decimalPart.length > 2) {
                                // Afkappen op 2 cijfers achter de komma (niet afronden, enkel trimmen)
                                decimalPart = decimalPart.substring(0, 2);
                            }
                            // Alleen weer de '.' toevoegen als er een decimaaldeel is (ook als dat nu ’00’ of korter is)
                            if (decimalPart.length > 0) {
                                formattedValue = integerPart + '.' + decimalPart;
                            } else {
                                // Gebruiker had bijvoorbeeld “15.” of “15.0” of “15.00” zonder extra decimals => integer houden
                                formattedValue = integerPart;
                            }
                        }
                        
                        e.target.value = formattedValue;
                        
                        var data = this.element.getData();
                        data.rows[this.data.id].record.particulate_weight = formattedValue
                        this.element.setData(data);
                    }.bind(this),
                    'blur': function(e) {
                        if (this.element.getData().rows[this.data.id].record.allergennotification.id == 1 && e.target.value == '') {
                            this.allergen_protein_content_element.addClass('forminputerror');
                        }
                    }.bind(this),
                    'focus': function(e) {
                        this.allergen_protein_content_element.removeClass('forminputerror');
                    }.bind(this)
                }
            });
            particulate_weight_element.addEvent('keypress', Actinum.DefaultEvents.isFloat);
            
            // Eiwitgehalte particulate
            var particulate_protein_content_element = this.particulate_protein_content_element = new Element('input', {
                'type' : 'text',
                'value':  this.data.record['particulate_protein_content'],
                'style' : 'padding: 5px; border-radius: 3px; border: 1px solid #d6d6d6; width: 120px;',
                'disabled' : meldingelement_value !== 3 || type_of_contamination_element_value !== 2,
                'required' : true,
                'events' : {
                    'change': function(e) {
                        var rawValue = e.target.value.trim();

                        // Check for negative values
                        if (parseFloat(rawValue) < 0) {
                            e.target.value = this.data.record['allergen_protein_content'];
                            Stratcom.notify('error', { 'message': 'Negatieve waarden zijn niet toegestaan.' });
                            return; 
                        }
                        
                        var formattedValue = rawValue;
                        
                        if (rawValue.indexOf('.') !== -1) {
                            var parts = rawValue.split('.');
                            var integerPart = parts[0];
                            var decimalPart = parts[1] || ''; // als gebruiker “12.” invoert, is parts[1] = ''
                            
                            if (decimalPart.length > 2) {
                                // Afkappen op 2 cijfers achter de komma (niet afronden, enkel trimmen)
                                decimalPart = decimalPart.substring(0, 2);
                            }
                            // Alleen weer de '.' toevoegen als er een decimaaldeel is (ook als dat nu ’00’ of korter is)
                            if (decimalPart.length > 0) {
                                formattedValue = integerPart + '.' + decimalPart;
                            } else {
                                // Gebruiker had bijvoorbeeld “15.” of “15.0” of “15.00” zonder extra decimals => integer houden
                                formattedValue = integerPart;
                            }
                        }
                        
                        e.target.value = formattedValue;
                        
                        var data = this.element.getData();
                        data.rows[this.data.id].record.particulate_protein_content = formattedValue
                        this.element.setData(data);
                    }.bind(this),
                    'blur': function(e) {
                        if (this.element.getData().rows[this.data.id].record.allergennotification.id == 1 && e.target.value == '') {
                            this.allergen_protein_content_element.addClass('forminputerror');
                        }
                    }.bind(this),
                    'focus': function(e) {
                        this.allergen_protein_content_element.removeClass('forminputerror');
                    }.bind(this)
                }
            });
            particulate_protein_content_element.addEvent('keypress', Actinum.DefaultEvents.isFloat);

        } else {
            var meldingelement = this.meldingelement = new Element('span', {
                'text': this.data.record['allergennotification'] ? this.data.record['allergennotification']['name'] : '',
                'style' : 'padding: 5px; border-radius: 3px;'
            });
            var ppmelement = this.ppmelement = new Element('span', {
                'text': this.data.record['ppm'],
                'style' : 'padding: 5px; border-radius: 3px;'
            });
            
            // Eiwitgehalte allergeen
            var allergen_protein_content_element = this.ppmelement = new Element('span', {
                'text': this.data.record['allergen_protein_content'],
                'style' : 'padding: 5px; border-radius: 3px;'
            });
            
            // Type besmetting
            var type_of_contamination_element = this.ppmelement = new Element('span', {
                'text': this.data.record['type_of_contamination'] ? this.data.record['type_of_contamination']['name'] : '',
                'style' : 'padding: 5px; border-radius: 3px;'
            });
            
            // PPM kruisbesmetting
            var ppm_cross_contamination_element = this.ppmelement = new Element('span', {
                'text': this.data.record['ppm_cross_contamination'],
                'style' : 'padding: 5px; border-radius: 3px;'
            });
            
            // Gewicht particulate
            var particulate_weight_element = this.ppmelement = new Element('span', {
                'text': this.data.record['particulate_weight'],
                'style' : 'padding: 5px; border-radius: 3px;'
            });
            
            // Eiwitgehalte particulate
            var particulate_protein_content_element = this.ppmelement = new Element('span', {
                'text': this.data.record['particulate_protein_content'],
                'style' : 'padding: 5px; border-radius: 3px;'
            });
        }

        var allergenMark = this.allergenMark = new Element('div');

        var tr = new Element('tr').adopt(
            new Element('td', {'text': this.data['name']}),
            new Element('td').adopt(allergenMark),
            new Element('td').adopt(meldingelement),
        );
        
        if (this.element.getData().ppmoption !== 3) {
            tr.adopt(
                new Element('td').adopt(ppmelement),
            )
        }
        
        if (this.element.getData().ppmoption === 3) {
            tr.adopt(
                new Element('td').adopt(allergen_protein_content_element),
                new Element('td').adopt(type_of_contamination_element),
                new Element('td').adopt(ppm_cross_contamination_element),
                new Element('td').adopt(particulate_weight_element),
                new Element('td').adopt(particulate_protein_content_element)
            )
        }

        if(this.element.getMode() != 'view') {
            this.updateVisiblityPPM();
        }

        return tr;
    },
    
    disableElement: function disableElement(el, field){
        el.setProperty('disabled', true);
        el.removeClass('forminputerror');
        
        var data = this.element.getData();
        data.rows[this.data.id].record[field] = null;
        this.element.setData(data);
        
        el.set('value', null);
    },
    
    enableElement: function enableElement(el, field){
        el.setProperty('disabled', false);
    }

});

JS;
        return $result;
    }



   public static function getCss(){
       $result = parent::getCss();
       $result['Actinum.Application.CustomCss.n10788n'] =
<<<'CSS'

    .n10788n .grid {
        overflow-x: auto;
        width: 100%;
    }
    
     .n10788n table { 
        table-layout: fixed; 
        width: 100% !important; 
        min-width: 800px; 
    } 
    
    .n10788n input:disabled {
        background-color: #d1d5dc !important;
    }
    .n10788n select:disabled {
        background-color: #d1d5dc !important;
    }

CSS;
       return $result;
   }


        public static function getDataForAdd(\nl\actinum\framework\application\DoctrineRecordWrapper $drw, array $settings) {
            $result = array();
            $result['rows'] = static::getRows($drw, $settings);
            $result['ppmenabled'] = static::getPPMEnabled($drw, $settings);
            $result['ppmoption'] = static::getPPMOption($drw, $settings);
            $result['allergen_policy_2024'] = static::getAllergenPolicy2024($drw, $settings);
            $result['contaminationtypes'] = static::getContaminationTypes($drw, $settings);
            $result['allergennotifications'] = static::getAllergennotification($drw, $settings);
            $result['allergenenindicatie'] = array();

            $result['rows'] = static::filterInactiveAllergens($result['rows']);
            return $result;
        }

        public static function getDataForEdit(\nl\actinum\framework\application\DoctrineRecordWrapper $drw, array $settings){
            $result = array();
            $result['rows'] = static::getRows($drw, $settings);
            $result['ppmenabled'] = static::getPPMEnabled($drw, $settings);
            $result['ppmoption'] = static::getPPMOption($drw, $settings);
            $result['allergen_policy_2024'] = static::getAllergenPolicy2024($drw, $settings);
            $result['contaminationtypes'] = static::getContaminationTypes($drw, $settings);
            $result['allergennotifications'] = static::getAllergennotification($drw, $settings);

            $drarray = $drw->getArray();
            $inkoopartikelid = $drarray['id'];
            $allergenenindicatie_wrapped = static::getAllergenenindicatie(array(), true, $inkoopartikelid);
            $result['allergenenindicatie'] = $allergenenindicatie_wrapped['allergenenindicatie'];

            $result['rows'] = static::filterInactiveAllergens($result['rows']);

            return $result;
        }

        public static function getDataForView(\nl\actinum\framework\application\DoctrineRecordWrapper $drw, array $settings){
            $result = array();
            $result['rows'] = static::getRows($drw, $settings);
            $result['ppmenabled'] = static::getPPMEnabled($drw, $settings);
            $result['ppmoption'] = static::getPPMOption($drw, $settings);
            $result['allergen_policy_2024'] = static::getAllergenPolicy2024($drw, $settings);
            $result['contaminationtypes'] = static::getContaminationTypes($drw, $settings);
            $result['allergennotifications'] = static::getAllergennotification($drw, $settings);

            $drarray = $drw->getArray();
            $inkoopartikelid = $drarray['id'];
            $allergenenindicatie_wrapped = static::getAllergenenindicatie(array(), true, $inkoopartikelid);
            $result['allergenenindicatie'] = $allergenenindicatie_wrapped['allergenenindicatie'];

            $result['rows'] = static::filterInactiveAllergens($result['rows']);

            return $result;
        }

        public static function getGrondstofids($inkoopartikelid){
            $em = static::getEM();
            $sql = 'SELECT baseproduct_id FROM purchaseitembaseproduct WHERE purchaseitem_id = :purchaseitem_id';
            $params = array(
                'purchaseitem_id' => $inkoopartikelid
            );
            $rows = $em->getConnection()->fetchAll($sql, $params);

            $result = array();
            foreach($rows as $row){
                $result[] = $row['baseproduct_id'];
            }

            return $result;
        }

        /** @\nl\actinum\framework\application\annotations\allowedFromClientSide */
        public static function getAllergenenindicatie($grondstofids, $fetchfromdb, $inkoopartikelid){
            if ($fetchfromdb){
                $grondstofids = static::getGrondstofids($inkoopartikelid);
            }

            $grondstofids_clean = array(0);
            foreach($grondstofids as $id){
                $grondstofids_clean[] = intval($id);
            }

            $allergenenindicatie = array(); //lijstje met allergeen ids die door deze grondstoffen gebruikt worden.
            $em = static::getEM();
            $query = $em->createQuery('
                SELECT
                    partial baseproduct.{id},
                    partial allergen.{id}
                FROM nl\actinum\custom\tables\baseproduct baseproduct
                LEFT JOIN baseproduct.allergens allergen
                WHERE baseproduct IN (:baseproducts)
            ');
            $query->setParameter('baseproducts', $grondstofids_clean);
            $rows = $query->getArrayResult();

            foreach($rows as $row){
                foreach($row['allergens'] as $allergen){
                    $allergenenindicatie[] = $allergen['id'];
                }
            }

            $allergenenindicatie = array_unique($allergenenindicatie);

            //print_r($allergenenindicatie);die();

            return array(
                'message' => '',
                'success' => true,
                'allergenenindicatie' => array_values($allergenenindicatie) //clientside hebben we een array[] nodig. Array unique preserveert keys, dus kan ineens een object{} worden.
            );
        }

        public static function getContaminationTypes(\nl\actinum\framework\application\DoctrineRecordWrapper $drw, array $settings){
            $em = static::getEM();

            $query = $em->createQuery('SELECT partial contaminationtypes.{id, name} FROM nl\actinum\custom\tables\contaminationtypes contaminationtypes ORDER BY contaminationtypes._ordering ASC');

            $list = $query->getArrayResult();

            return $list;
        }

        public static function getAllergennotification(\nl\actinum\framework\application\DoctrineRecordWrapper $drw, array $settings){
            $em = static::getEM();

//            $ssdrw = \nl\actinum\framework\application\Application::getDefaultSystemSettings();
//            $ss = $ssdrw->getArray();

//            if($ss['ppmenabled'] == false){
//                $query = $em->createQuery('SELECT partial allergennotification.{id, name} FROM nl\actinum\custom\tables\allergennotification allergennotification WHERE allergennotification.id != 5 ORDER BY allergennotification._ordering ASC');
//            } else {
                $query = $em->createQuery('SELECT partial allergennotification.{id, name} FROM nl\actinum\custom\tables\allergennotification allergennotification ORDER BY allergennotification._ordering ASC');
//            }

            $list = $query->getArrayResult();


            return $list;
        }

        public static function getRows(\nl\actinum\framework\application\DoctrineRecordWrapper $drw, array $settings) {
            $em = static::getEM();
            //vergelijkbaar met een linkform.

            //1 met alle opties ...
            $query = $em->createQuery('
                SELECT
                    PARTIAL allergen.{id, name, _ordering}
                FROM
                    \nl\actinum\custom\tables\allergen allergen
            ');
            $all_indexed = array();
            $all = $query->getArrayResult();
            foreach($all as $x){
                $all_indexed[$x['id']] = $x;
            }


            if ($drw->isLoaded()){
                $drarray = $drw->getArray();
                $purchaseitem_id = $drarray['id'];

                //... en 2e met alle ingevulde waardes
                $query = $em->createQuery('
                    SELECT
                        PARTIAL purchaseitemallergen.{id, ppm, allergen_protein_content, ppm_cross_contamination, particulate_weight, particulate_protein_content},
                        PARTIAL allergen.{id},
                        PARTIAL allergennotification.{id, name},
                        PARTIAL type_of_contamination.{id, name}
                    FROM
                        \nl\actinum\custom\tables\purchaseitemallergen purchaseitemallergen
                    LEFT JOIN
                        purchaseitemallergen.allergen allergen
                    LEFT JOIN
                        purchaseitemallergen.allergennotification allergennotification
                    LEFT JOIN
                        purchaseitemallergen.type_of_contamination type_of_contamination
                    WHERE
                        purchaseitemallergen.purchaseitem = :purchaseitem
                ');
                $query->setParameter('purchaseitem', $purchaseitem_id);
                $myvalues = $query->getArrayResult();

                //reorder by other.id
                $myvalues_indexed = array();
                foreach($myvalues as $myvalue){
                    $id = $myvalue['allergen']['id'];
                    unset($myvalue['allergen']);
                    $myvalues_indexed[$id] = $myvalue;
                }
            }
            else{
                $myvalues_indexed = array();
            }


            foreach($all_indexed as $key => $row) {
                if (array_key_exists($row['id'], $myvalues_indexed)){
                    $all_indexed[$key]['record'] = $myvalues_indexed[$row['id']];
                }
                else{
                    $all_indexed[$key]['record'] = array(
                        'id' => null,
                        'ppm' => '',
                        'allergen_protein_content' => '',
                        'ppm_cross_contamination' => '',
                        'particulate_weight' => '',
                        'particulate_protein_content' => '',
                        'allergennotification' => array(
                            'id' => null,
                            'name' => '',
                        ),
                        'type_of_contamination' => array(
                            'id' => null,
                            'name' => '',
                        )
                    );
                }
            }

            return $all_indexed;
        }

        public static function getPPMEnabled(\nl\actinum\framework\application\DoctrineRecordWrapper $drw, array $settings) {
            $ppmoption = self::getPPMOption($drw, $settings);

            return $ppmoption === 2;
        }

        public static function getPPMOption(\nl\actinum\framework\application\DoctrineRecordWrapper $drw, array $settings) {
            $systemsettings = static::getEM()->find('nl\actinum\custom\tables\systemsettings', 1);
            $ppmoption = $systemsettings->getPPMOption();

            return $ppmoption ? $ppmoption->getId() : null;
        }

        public static function getAllergenPolicy2024(\nl\actinum\framework\application\DoctrineRecordWrapper $drw, array $settings) {
            $ssdrw = \nl\actinum\framework\application\Application::getDefaultSystemSettings();
            $ss = $ssdrw->getArray();

            return $ss['specifications_allergen_policy_2024'] == true ? true : false;
        }

        public static function saveData($data, \nl\actinum\framework\application\DoctrineRecordWrapper $drw, array $settings){
            $em = static::getEM();

            foreach($data as $d){
                $record = $d['record'];

                if(is_null($record['id'])){
                    $purchaseitem = $drw->getObject();
                    $allergen = $em->find('nl\actinum\custom\tables\allergen', $d['id']);

                    //het kan zijn dat er ondertussen door een andere pagina een entry is opgeslagen. Hiervoor moeten we eerst controleren.
                    $records = $em->getRepository('nl\actinum\custom\tables\purchaseitemallergen')->findBy(array('purchaseitem' => $purchaseitem->getId(), 'allergen' => $allergen->getId()));
                    if (count($records) > 0){
                        $purchaseitemallergen = $records[0];
                    }
                    else{
                        $purchaseitemallergen = new \nl\actinum\custom\tables\purchaseitemallergen();
                        $purchaseitemallergen->setPurchaseitem($purchaseitem);
                        $purchaseitemallergen->setAllergen($allergen);
                    }
                }
                else{
                    $purchaseitemallergen = $em->find('nl\actinum\custom\tables\purchaseitemallergen', $record['id']);
                }

                if ($purchaseitemallergen){
                    $allergennotification = null;
                    $allergennotification_id = $record['allergennotification']['id'];
                    if ($allergennotification_id){
                        $allergennotification = $em->getReference('nl\actinum\custom\tables\allergennotification', $allergennotification_id);
                    }
                    $purchaseitemallergen->setAllergennotification($allergennotification);

                    $ppmoption = self::getPPMOption($drw, $settings); // moeten we hier nog wat mee voor de zekerheid?

                    if ($ppmoption !== 3) {
                        $ppm = floatval($record['ppm']);
                        $purchaseitemallergen->setPpm($ppm);
                    }

                    if ($ppmoption === 3) {


                        if($allergennotification === null) {
                            $allergen_protein_content = null;
                            $purchaseitemallergen->setAllergen_protein_content(null);
                            $purchaseitemallergen->setType_of_contamination(null);
                            $purchaseitemallergen->setPpm_cross_contamination(null);
                            $purchaseitemallergen->setParticulate_weight(null);
                            $purchaseitemallergen->setParticulate_protein_content(null);
                        }

                        if ($allergennotification && $allergennotification->getId() === 1) {
                            // save values for this type of allergennotification
                            $allergen_protein_content = floatval($record['allergen_protein_content']);
                            $purchaseitemallergen->setAllergen_protein_content($allergen_protein_content);

                            // reset the other values
                            $purchaseitemallergen->setType_of_contamination(null);
                            $purchaseitemallergen->setPpm_cross_contamination(null);
                            $purchaseitemallergen->setParticulate_weight(null);
                            $purchaseitemallergen->setParticulate_protein_content(null);
                        }

                        else if ($allergennotification && $allergennotification->getId() === 2 ) {
                            $purchaseitemallergen->setAllergen_protein_content(null);
                            $purchaseitemallergen->setType_of_contamination(null);
                            $purchaseitemallergen->setPpm_cross_contamination(null);
                            $purchaseitemallergen->setParticulate_weight(null);
                            $purchaseitemallergen->setParticulate_protein_content(null);
                        }


                        else if ($allergennotification && $allergennotification->getId() === 3 &&  $record['type_of_contamination']['id']) {
                            // save values for this type of allergennotification
                            $type_of_contamination = null;
                            $type_of_contamination_id = $record['type_of_contamination']['id'];
                            if ($type_of_contamination_id) {
                                $type_of_contamination = $em->getReference('nl\actinum\custom\tables\contaminationtypes', $type_of_contamination_id);
                            }
                            $purchaseitemallergen->setType_of_contamination($type_of_contamination);

                            if ($type_of_contamination->getId() === 1) {
                                // save values for this type of contamination
                                $ppm_cross_contamination = floatval($record['ppm_cross_contamination']);
                                $purchaseitemallergen->setPpm_cross_contamination($ppm_cross_contamination);

                                // reset the other values
                                $purchaseitemallergen->setParticulate_weight(null);
                                $purchaseitemallergen->setParticulate_protein_content(null);
                            }

                            else if ($type_of_contamination->getId() === 2) {
                                // save values for this type of contamination
                                $particulate_weight = floatval($record['particulate_weight']);
                                $purchaseitemallergen->setParticulate_weight($particulate_weight);

                                $particulate_protein_content = floatval($record['particulate_protein_content']);
                                $purchaseitemallergen->setParticulate_protein_content($particulate_protein_content);

                                // reset the other values
                                $purchaseitemallergen->setPpm_cross_contamination(null);
                            }

                            // reset the other values
                            $purchaseitemallergen->setAllergen_protein_content(null);
                        }
                    }

                    $em->persist($purchaseitemallergen);
                }
            }
        }

        public static function validate($data) {
            return true;
        }

        public static function validateAndSaveData($data, \nl\actinum\framework\application\DoctrineRecordWrapper $drw, array $settings){
            static::saveData($data, $drw, $settings);
        }

        public static function getMergedSettingsWithDefaultSettings(\nl\actinum\framework\application\DoctrineRecordWrapper $drw, array $settings){
            return $settings;
        }

        public static function filterInactiveAllergens($data)
        {
            $inactiveAllergens = n12595n::getInActiveAllergens();

            return array_filter($data, function($item) use ($inactiveAllergens) {
                return ! in_array($item['id'], $inactiveAllergens) || $item['record']['allergennotification']['id'] === 1 || $item['record']['allergennotification']['id'] === 3;
            });
        }


}

}
?>