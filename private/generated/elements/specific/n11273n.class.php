<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Gridpreset
class n11273n extends \nl\actinum\custom\elements\generic\Gridpreset {

    
    public static $title = 'Afnemers';
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n11273n';
    public static $parentElementid = 'n9252n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n11274n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n11273n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n11273n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>