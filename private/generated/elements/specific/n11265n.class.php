<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Forminputcontainer
class n11265n extends \nl\actinum\custom\elements\generic\Forminputcontainer {

    
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n11265n';
    public static $parentElementid = 'n11264n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
  0 => 'n11266n',
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n11265n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n11265n'] =
<<<'EOD'

EOD;
        return $result;
    }
    
    


}
}
?>