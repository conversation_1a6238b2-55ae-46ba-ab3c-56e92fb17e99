<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Gridcolumnstring
class n11286n extends \nl\actinum\custom\elements\generic\Gridcolumnstring {

    
    public static $dbfield = 'email';
    public static $addlink = false;
    public static $disabledadvancedsearch = NULL;
    public static $title = 'E-mail';
    public static $show = true;
    public static $dbtrigger = NULL;
    public static $mappedforminput = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n11286n';
    public static $parentElementid = 'n9818n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n11286n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n11286n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>