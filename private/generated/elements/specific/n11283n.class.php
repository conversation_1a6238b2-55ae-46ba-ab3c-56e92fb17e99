<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Forminputstring
class n11283n extends \nl\actinum\custom\elements\generic\Forminputstring {

    
    public static $subtitle = 'e-mail';
    public static $showsubtitle = NULL;
    public static $dbfield = 'email';
    public static $defaultvalue = NULL;
    public static $nullallowed = NULL;
    public static $dbtrigger = NULL;
    public static $index = NULL;
    public static $placeholder = NULL;
    public static $required = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n11283n';
    public static $parentElementid = 'n11281n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n11283n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n11283n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>