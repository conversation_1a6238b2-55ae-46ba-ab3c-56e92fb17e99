<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Forminputcontainerlabel
class n11282n extends \nl\actinum\custom\elements\generic\Forminputcontainerlabel {

    
    public static $title = 'E-mail';
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n11282n';
    public static $parentElementid = 'n11281n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n11282n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n11282n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>