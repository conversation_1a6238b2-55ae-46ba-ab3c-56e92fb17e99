<?php
namespace nl\actinum\generated\elements\specific {

//Element of type: Forminputimage
class n11266n extends \nl\actinum\custom\elements\generic\Forminputimage {

    
    public static $aliaspostfix = NULL;
    public static $maxfilesize = 2097152;
    public static $maxfiles = '0';
    public static $maxthumbwidth = '200';
    public static $maxthumbheight = '200';
    public static $subtitle = 'Foto';
    public static $showsubtitle = true;
    public static $dbfield = 'photo';
    public static $defaultvalue = '';
    public static $nullallowed = NULL;
    public static $dbtrigger = NULL;
    public static $index = NULL;
    public static $placeholder = NULL;
    public static $required = NULL;
    public static $style = NULL;
    public static $visibilityconditionjavascript = NULL;
    public static $elementid = 'n11266n';
    public static $parentElementid = 'n11265n';
    public static $visibleonadd = true;
    public static $visibleonedit = true;
    public static $visibleonview = true;
    public static $inlinehelptext = NULL;
    
    public static $childrenElementIds = array (
);

    public static function getJs(){
        $result = parent::getJs();
        $result['Actinum.Application.GeneratedJs.n11266n'] =
<<<'EOD'
    
EOD;
        return $result;
    }


    public static function getCss(){
        $result = parent::getCss();
        $result['Actinum.Application.GeneratedCss.n11266n'] =
<<<'EOD'

EOD;
        return $result;
    }


}
}
?>