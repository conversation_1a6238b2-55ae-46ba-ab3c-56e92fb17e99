<?php
namespace nl\actinum\generated\metadata;

class MetaData {

    //static methods and function providing hard to get runtime information from the model
    //such as the first Element, the generation time, the size of the model,
    //the number of elements per type, etc.

    
    public static $savedon = '05/22/2015 3:49PM';
    public static $elementcounts = array (
  'Element' => 0,
  'StructuralElement' => 0,
  'CanvasElement' => 0,
  'DivCanvasElement' => 0,
  'SvgCanvasElement' => 0,
  'Forminput' => 0,
  'StatediagramNode' => 0,
  'StatediagramConnection' => 0,
  'Forminputsearch' => 43,
  'Action' => 0,
  'Actionsequence' => 0,
  'Barcodescannerdetector' => 0,
  'Block' => 39,
  'Block1ton' => 0,
  'Blockcontainer' => 367,
  'Blockcontainershowhide' => 0,
  'Blockpopup' => 81,
  'Blocktab' => 37,
  'Button' => 0,
  'Buttonadd' => 31,
  'Buttonbar' => 61,
  'Buttoncancel' => 0,
  'Buttoncancelgridrowform' => 73,
  'Buttoncustom' => 62,
  'Buttonedit' => 44,
  'Buttonlink' => 0,
  'Buttonsubmit' => 42,
  'Buttonsubmitandnew' => 7,
  'Buttonsubmitgridrowform' => 75,
  'Colorpicker' => 6,
  'Datepicker' => 51,
  'Datetimepicker' => 7,
  'Doctrinefilter' => 271,
  'Doctrineorderby' => 36,
  'Dutchzipcodewidget' => 0,
  'Emailform' => 0,
  'Form' => 167,
  'Forminputcolor' => 6,
  'Forminputcontainer' => 745,
  'Forminputcontainerlabel' => 541,
  'Forminputcountry' => 0,
  'Forminputdate' => 48,
  'Forminputdatetime' => 9,
  'Forminputdropdown' => 166,
  'Forminputemail' => 3,
  'Forminputfile' => 6,
  'Forminputfilemultiple' => 11,
  'Forminputfloat' => 18,
  'Forminputgroup' => 19,
  'Forminputimage' => 7,
  'Forminputimagemultiple' => 0,
  'Forminputint' => 57,
  'Forminputintautoincrement' => 0,
  'Forminputmoney' => 3,
  'Forminputpassword' => 3,
  'Forminputpercentage' => 9,
  'Forminputradiobutton' => 0,
  'Forminputsearchmultiple' => 11,
  'Forminputstreetnumber' => 2,
  'Forminputstring' => 265,
  'Forminputtelephone' => 0,
  'Forminputtext' => 96,
  'Forminputtextmarkdown' => 0,
  'Forminputtime' => 6,
  'Forminputurl' => 0,
  'Forminputyesno' => 77,
  'Forminputyesnounknown' => 0,
  'Forminputzipcode' => 2,
  'Googlemaps' => 0,
  'Googlestreetview' => 0,
  'Greaterthanvalidationrule' => 0,
  'Grid' => 95,
  'Gridaddoncontainer' => 85,
  'Gridadvancedsearchbox' => 0,
  'Gridcollapsible' => 2,
  'Gridcolumn' => 0,
  'Gridcolumncolor' => 6,
  'Gridcolumncountry' => 0,
  'Gridcolumndate' => 46,
  'Gridcolumndatetime' => 9,
  'Gridcolumndropdown' => 53,
  'Gridcolumnemail' => 1,
  'Gridcolumnfile' => 0,
  'Gridcolumnfilemultiple' => 5,
  'Gridcolumnfloat' => 12,
  'Gridcolumnimage' => 2,
  'Gridcolumnint' => 55,
  'Gridcolumnlinkform' => 1,
  'Gridcolumnmoney' => 1,
  'Gridcolumnpassword' => 0,
  'Gridcolumnpercentage' => 10,
  'Gridcolumnradiobutton' => 0,
  'Gridcolumnsearch' => 45,
  'Gridcolumnsearchmultiple' => 5,
  'Gridcolumnstreetnumber' => 0,
  'Gridcolumnstring' => 205,
  'Gridcolumntelephone' => 0,
  'Gridcolumntext' => 43,
  'Gridcolumntextmarkdown' => 0,
  'Gridcolumntime' => 0,
  'Gridcolumnurl' => 0,
  'Gridcolumnyesno' => 38,
  'Gridcolumnyesnounknown' => 6,
  'Gridcolumnzipcode' => 0,
  'Griddatenavigator' => 0,
  'Gridexport' => 14,
  'Gridpreset' => 135,
  'Gridrowaddlink' => 0,
  'Gridrowdeletelink' => 142,
  'Gridrowdeleteprotectedlink' => 0,
  'Gridroweditlink' => 136,
  'Gridrowform' => 74,
  'Gridsearchbox' => 85,
  'Gridselectioncustomaction' => 32,
  'Gridselectiondeleteaction' => 0,
  'Gridselectionlinkaction' => 0,
  'Gridselectionunlinkaction' => 0,
  'Header1' => 150,
  'Header2' => 0,
  'Header3' => 84,
  'Helppopup' => 21,
  'Highcharts_columnchart' => 0,
  'Highcharts_linechart' => 3,
  'Highcharts_piechart' => 0,
  'Historyinfo' => 17,
  'Html' => 130,
  'Linkform' => 35,
  'Linkgrid' => 0,
  'Mobilemodule' => 1,
  'Module' => 6,
  'Nextpreviousbuttons' => 0,
  'Onetimeinfo' => 0,
  'Page' => 33,
  'Pagegroup' => 35,
  'Plusbuttonform' => 3,
  'Plusbuttongrid' => 88,
  'Plusbuttongridcollapsible' => 2,
  'Plusbuttonhtml' => 0,
  'Preferencemodule' => 2,
  'State' => 80,
  'Statediagram' => 80,
  'Subpage' => 142,
  'Subpagegroup' => 111,
  'Subpagelink' => 123,
  'Subpagelinkcontainer' => 37,
  'Systemlevel' => 0,
  'Systemlevelindicator' => 0,
  'Tab' => 115,
  'Tabcontainer' => 35,
  'Tablepage' => 79,
  'Tasklink' => 71,
  'Tasklinkcontainer' => 68,
  'Tasklinkdownloaddocument' => 7,
  'Timeline' => 4,
  'Timepicker' => 2,
  'Timewriterformday' => 0,
  'Timewriterformweek' => 0,
  'Trigger' => 0,
  'Url' => 13,
  'Userpreferencemodule' => 1,
  'Warningarea' => 0,
  'Webapp' => 1,
);
    public static $webappelementid = 'n2n';
    public static $projectid = 111;
    public static $buildid = 10953;
    public static $studioversion_builder = 'trunk';
}
?>