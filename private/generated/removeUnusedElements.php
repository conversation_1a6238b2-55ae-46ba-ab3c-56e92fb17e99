<?php
echo 'Edit file to actually remove the files below';
$data_compressed = file_get_contents('model.json');
$data = gzuncompress($data_compressed);
$obj = json_decode($data, true);
print '<pre>';
//print_r($obj);

$GLOBALS['elements'] = array();
findElements($obj);
$list = array_values($GLOBALS['elements']);
//print_r($list);

removeFiles("../custom/elements/specific", $list);
removeFiles("elements/specific", $list);

function findElements($obj){
	$elementid = $obj['properties']['elementid'];
	$GLOBALS['elements'][$elementid] = $elementid;
	foreach($obj['nodes'] as $node){
		findElements($node);
	}
}


function removeFiles($dir, $keep){
    $myDirectory = opendir($dir);
    $dirArray = array();
    while($entryName = readdir($myDirectory)) {
        if ($entryName != '.' && $entryName != '..'){
            $frags = explode(".", $entryName);
            $dirArray[] = $frags[0];
        }
    }

    $toremove = array();
    foreach($dirArray as $el){
        if(!in_array($el, $keep)){
            $toremove[] = $el;
        }
    }

    print_r($toremove);

    foreach($toremove as $f){
        //unlink($dir."/".$f.'.class.php');
    }
}



?>