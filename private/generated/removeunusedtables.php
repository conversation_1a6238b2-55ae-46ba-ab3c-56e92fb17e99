<?php
echo 'Edit file to actually remove the files below';
include('tables.php');
print '<pre>';

removeFiles('tables', $tables);
removeFiles('../custom/tables', $tables);

function removeFiles($dir, $keep){
    $myDirectory = opendir($dir);
    $dirArray = array();
    while($entryName = readdir($myDirectory)) {
        if ($entryName != '.' && $entryName != '..'){
            $frags = explode(".", $entryName);
            $dirArray[] = $frags[0];
        }
    }

    $toremove = array();
    foreach($dirArray as $table){
        if(!in_array($table, $keep)){
            $toremove[] = $table;
        }
    }

    print_r($toremove);

    foreach($toremove as $f){
        //unlink($dir.'/'.$f.'.class.php');
    }
}
?>